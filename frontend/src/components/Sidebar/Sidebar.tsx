import React, { useState, useEffect } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Typography,
  Avatar,
  Badge,
  Tooltip,
  useTheme,
  Collapse,
  CircularProgress,
  Chip,
  IconButton,
} from '@mui/material';
import {
  History as HistoryIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Logout as LogoutIcon,
  Login as LoginIcon,
  AccessTime as AccessTimeIcon,
  Calculate as CalculateIcon,
  Chat as ChatIcon,
  ExpandLess,
  ExpandMore,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  getSessionSummaries,
  createSession,
  setActiveSession,
  getActiveSession,
  deleteSession,
  getSession,
  saveSessionIfNeeded,
  isInitialState,
  createInitialWelcomeSession
} from '../../services/chatSessionStorage';
import { ChatSessionSummary } from '../../types/chatSession';
import { formatDistanceToNow } from 'date-fns';
import { useLanguage } from '../../contexts/LanguageContext';

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, loading: authLoading } = useAuth();
  const { t } = useLanguage();
  const theme = useTheme();
  const [sessions, setSessions] = useState<ChatSessionSummary[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isCreatingSession, setIsCreatingSession] = useState<boolean>(false);
  const [isDeletingSession, setIsDeletingSession] = useState<Record<string, boolean>>({});
  const [isHistoryOpen, setIsHistoryOpen] = useState<boolean>(true);

  // Debug user state
  useEffect(() => {
    console.log('User state in Sidebar:', user);
  }, [user]);

  const isActive = (path: string) => location.pathname === path;

  // Load chat sessions
  // Function to load sessions - defined outside useEffect so it can be called from other places
  const loadSessions = async () => {
    setIsLoading(true);
    try {
      console.log('Loading chat sessions...');

      // Get sessions from storage - this will include sessions with user messages AND the active session
      const loadedSessions = await getSessionSummaries();
      console.log(`Loaded ${loadedSessions.length} chat sessions`);

      // Just validate that each session has a valid messages array
      const validSessions = loadedSessions.filter(session => {
        // Make sure session has messages array
        if (!session.messages || !Array.isArray(session.messages)) {
          console.warn(`Session ${session.id} has invalid messages property`);
          return false;
        }
        return true;
      });

      console.log(`Displaying ${validSessions.length} sessions in sidebar`);
      setSessions(validSessions);

      // Get active session
      try {
        const currentSessionId = await getActiveSession();
        console.log('Current active session ID:', currentSessionId);
        setActiveSessionId(currentSessionId);
      } catch (error) {
        console.error('Failed to get active session:', error);
      }
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initial load of sessions - only when the component mounts
    console.log('Initial loading of chat sessions');
    loadSessions();

    // Add event listener for chat updates
    const handleChatUpdate = () => {
      console.log('=== SIDEBAR: Chat updated event received ===');
      console.log('Current sessions in state:', sessions);
      console.log('Current active session ID:', activeSessionId);
      // Reload sessions when a chat update event is received
      loadSessions();
    };

    // Listen for custom events that signal chat changes
    window.addEventListener('chat-updated', handleChatUpdate);

    return () => {
      window.removeEventListener('chat-updated', handleChatUpdate);
    };
  }, []); // Empty dependency array to only run on mount

  const handleNewConversion = async () => {
    setIsCreatingSession(true);
    try {
      console.log('Creating new conversion session');

      // Check if current session should be saved before creating a new one
      let currentSessionId;
      try {
        currentSessionId = await getActiveSession();
        console.log('Current active session ID:', currentSessionId);
      } catch (error) {
        console.error('Failed to get active session:', error);
        currentSessionId = null;
      }

      // If we have a current session, check if it's already in initial state
      if (currentSessionId) {
        try {
          const currentSession = await getSession(currentSessionId);

          // If current session is already in initial state, do nothing
          if (currentSession && isInitialState(currentSession)) {
            console.log('Already in a new chat state, no need to create another one');
            setIsCreatingSession(false);
            // Still navigate to home to ensure we're on the chat page
            navigate('/');
            return;
          }

          // Force save the current session if it has user messages
          if (currentSession && currentSession.messages.some(msg => msg.type === 'user')) {
            console.log('Current session has user messages, saving before creating new session');
            await saveSessionIfNeeded(currentSessionId);
          }
        } catch (error) {
          console.error('Error checking current session:', error);
          // Continue with creating a new session anyway
        }
      }

      // Create a new welcome session directly
      // The welcome message is created inside createInitialWelcomeSession
      console.log('Creating new session with welcome message');
      const newSession = await createInitialWelcomeSession();
      console.log('Created new session with ID:', newSession.id);

      // Set the new session as active
      await setActiveSession(newSession.id);
      console.log('Set new session as active:', newSession.id);

      // Update active session ID in component state
      setActiveSessionId(newSession.id);

      // Force refresh the sessions list to include the new session
      console.log('Refreshing sessions list after creating new session');
      await loadSessions();

      // Trigger a chat update event
      window.dispatchEvent(new CustomEvent('chat-updated'));

      // Navigate to home
      navigate('/');
    } catch (error) {
      console.error('Failed to create new session:', error);
    } finally {
      setIsCreatingSession(false);
    }
  };

  const handleSessionClick = async (sessionId: string) => {
    console.log(`Switching to session: ${sessionId}`);
    // Only process if it's not already the active session
    if (sessionId !== activeSessionId) {
      try {
        // Try to save the current session before switching if it has user messages
        const currentSessionId = activeSessionId;
        if (currentSessionId) {
          try {
            const currentSession = await getSession(currentSessionId);
            if (currentSession && currentSession.messages.some(msg => msg.type === 'user')) {
              console.log(`Saving current session ${currentSessionId} before switching`);
              await saveSessionIfNeeded(currentSessionId);
            }
          } catch (error) {
            console.error('Error checking current session:', error);
          }
        }

        // Set the new active session
        console.log(`Setting new active session: ${sessionId}`);
        await setActiveSession(sessionId);
        setActiveSessionId(sessionId);

        // Trigger a chat update event to refresh the UI
        window.dispatchEvent(new CustomEvent('chat-updated'));

        // Navigate to home page to show the chat
        navigate('/');
      } catch (error) {
        console.error('Failed to switch sessions:', error);
      }
    } else {
      console.log(`Session ${sessionId} is already active, no need to switch`);
      // Still navigate to home to ensure we're on the chat page
      navigate('/');
    }
  };

  const handleDeleteSession = async (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation(); // Prevent session click
    console.log(`Deleting session: ${sessionId}`);

    // Set loading for specific session
    setIsDeletingSession(prev => ({
      ...prev,
      [sessionId]: true
    }));

    try {
      // Check if this is the active session
      const isActiveSession = sessionId === activeSessionId;
      console.log(`Is active session: ${isActiveSession}`);

      // Delete the session
      const success = await deleteSession(sessionId);
      console.log(`Delete session result: ${success}`);

      if (success) {
        // Reload sessions - this should only show sessions with user messages
        console.log('Refreshing sessions list after deletion');
        await loadSessions();

        // If we deleted the active session, we need to get the new active session
        if (isActiveSession) {
          try {
            const currentSessionId = await getActiveSession();
            console.log('Current active session after deletion:', currentSessionId);
            setActiveSessionId(currentSessionId);

            // Always navigate to home to refresh the UI
            // This ensures we see the new welcome message if we deleted the active session
            navigate('/', { replace: true });
          } catch (error) {
            console.error('Failed to get active session after deletion:', error);
            // Navigate home anyway as a fallback
            navigate('/', { replace: true });
          }
        }
      } else {
        console.error('Failed to delete session - deleteSession returned false');
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
    } finally {
      // Clear loading state
      setIsDeletingSession(prev => ({
        ...prev,
        [sessionId]: false
      }));
    }
  };

  const handleLogout = () => {
    logout();
    // Force a page reload to ensure all components update
    setTimeout(() => {
      window.location.reload();
    }, 100);
    navigate('/login');
  };

  // Function to get the icon for a session based on its function type
  const getSessionIcon = (functionType?: string) => {
    switch (functionType) {
      case 'conversion':
        return <CalculateIcon fontSize="small" />;
      case 'table':
        return <HistoryIcon fontSize="small" />;
      default:
        return <ChatIcon fontSize="small" />;
    }
  };

  // Format a relative time string (e.g., "2 days ago")
  const formatRelativeTime = (date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true });
  };

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* User profile section - removed from top as it's now in the bottom */}

      <Box sx={{
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        gap: 2
      }}>
        <Button
          fullWidth
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleNewConversion}
          disabled={isCreatingSession || authLoading}
          sx={{
            mb: 2,
            borderColor: theme.palette.divider,
            color: theme.palette.text.primary,
            fontSize: '0.8rem'
          }}
        >
          {isCreatingSession ? t('creatingSession') : t('newConversionTitle')}
        </Button>
      </Box>

      <List component="nav" sx={{ px: 1, flexGrow: 1, overflowY: 'auto' }}>
        {/* Only show history for authenticated users */}
        {user && (
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              onClick={() => setIsHistoryOpen(!isHistoryOpen)}
              sx={{
                borderRadius: '0.375rem',
                mb: 0.5,
                color: theme.palette.sidebarText,
                '&:hover': {
                  backgroundColor: theme.palette.sidebarHoverBg,
                },
              }}
            >
              <ListItemIcon sx={{ color: theme.palette.sidebarText, minWidth: 36 }}>
                <HistoryIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary={t('history')} primaryTypographyProps={{ fontSize: '0.75rem' }} />
              {isHistoryOpen ? <ExpandLess /> : <ExpandMore />}
            </ListItemButton>

            <Collapse in={isHistoryOpen} timeout="auto" unmountOnExit>
              {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                  <CircularProgress size={24} sx={{ color: theme.palette.sidebarText }} />
                </Box>
              ) : sessions.length === 0 ? (
                <Box sx={{ p: 2, color: theme.palette.sidebarTextSecondary, fontSize: '0.75rem', textAlign: 'center' }}>
                  {t('noHistory')}
                </Box>
              ) : (
                <List component="div" disablePadding>
                  {sessions.map((session) => (
                  <ListItemButton
                    key={session.id}
                    selected={activeSessionId === session.id}
                    onClick={() => handleSessionClick(session.id)}
                    disabled={isDeletingSession[session.id]}
                    sx={{
                      pl: 4,
                      borderRadius: '0.375rem',
                      mb: 0.5,
                      ml: 1,
                      color: theme.palette.sidebarText,
                      '&:hover': {
                        backgroundColor: theme.palette.sidebarHoverBg,
                      },
                      '&.Mui-selected': {
                        backgroundColor: theme.palette.sidebarSelectedBg,
                        '&:hover': {
                          backgroundColor: theme.palette.sidebarSelectedBg,
                        }
                      }
                    }}
                  >
                    <ListItemIcon sx={{ color: theme.palette.sidebarText, minWidth: 36 }}>
                      {getSessionIcon(session.function)}
                    </ListItemIcon>
                    <ListItemText
                      primary={session.title}
                      secondary={formatRelativeTime(session.lastUpdated)}
                      primaryTypographyProps={{
                        noWrap: true,
                        fontSize: '0.75rem',
                      }}
                      secondaryTypographyProps={{
                        noWrap: true,
                        fontSize: '0.7rem',
                        sx: { color: theme.palette.sidebarTextSecondary }
                      }}
                    />
                    <Tooltip title={t('deleteSessionTooltip')}>
                      {isDeletingSession[session.id] ? (
                        <CircularProgress size={20} sx={{ color: theme.palette.sidebarTextSecondary }} />
                      ) : (
                        <IconButton
                          edge="end"
                          onClick={(e) => handleDeleteSession(e, session.id)}
                          sx={{ color: theme.palette.sidebarTextSecondary, '&:hover': { color: theme.palette.sidebarText } }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      )}
                    </Tooltip>
                  </ListItemButton>
                ))}
              </List>
            )}
          </Collapse>
        </ListItem>
        )}

        <ListItem disablePadding>
          <ListItemButton
            onClick={() => navigate('/')}
            selected={isActive('/')}
            sx={{
              borderRadius: '0.375rem',
              mb: 0.5,
              color: theme.palette.sidebarText,
              '&:hover': {
                backgroundColor: theme.palette.sidebarHoverBg,
              },
              '&.Mui-selected': {
                backgroundColor: theme.palette.sidebarSelectedBg,
                '&:hover': {
                  backgroundColor: theme.palette.sidebarSelectedBg,
                }
              }
            }}
          >
            <ListItemIcon sx={{ color: theme.palette.sidebarText, minWidth: 36 }}>
              <CalculateIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary={t('converter')} primaryTypographyProps={{ fontSize: '0.8rem' }} />
          </ListItemButton>
        </ListItem>

        <ListItem disablePadding>
          <ListItemButton
            onClick={() => navigate('/settings')}
            selected={isActive('/settings')}
            sx={{
              borderRadius: '0.375rem',
              mb: 0.5,
              color: theme.palette.sidebarText,
              '&:hover': {
                backgroundColor: theme.palette.sidebarHoverBg,
              },
              '&.Mui-selected': {
                backgroundColor: theme.palette.sidebarSelectedBg,
                '&:hover': {
                  backgroundColor: theme.palette.sidebarSelectedBg,
                }
              }
            }}
          >
            <ListItemIcon sx={{ color: theme.palette.sidebarText, minWidth: 36 }}>
              <SettingsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary={t('settings')} primaryTypographyProps={{ fontSize: '0.8rem' }} />
          </ListItemButton>
        </ListItem>
      </List>

      <Box sx={{ p: 1.5, mt: 'auto' }}>
        <Divider sx={{ bgcolor: theme.palette.divider, my: 1 }} />

        {!authLoading && user ? (
          <Box sx={{ mt: 2 }}>
            <Box sx={{
              p: 1.5,
              mb: 1,
              borderRadius: '0.375rem',
              backgroundColor: theme.palette.sidebarHoverBg,
              color: theme.palette.sidebarText,
              border: `1px solid ${theme.palette.sidebarTextSecondary}`
            }}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Avatar
                  sx={{
                    bgcolor: '#404040',
                    color: '#ececec',
                    width: 32,
                    height: 32,
                    fontSize: '0.9rem'
                  }}
                >
                  {user.username?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
                </Avatar>
                <Box sx={{ overflow: 'hidden', maxWidth: 'calc(100% - 40px)' }}>
                  <Typography variant="body2" fontWeight="medium" noWrap>
                    {user.username || t('user')}
                  </Typography>
                  <Tooltip title={user.email} placement="top">
                    <Typography variant="caption" noWrap sx={{ opacity: 0.9, display: 'block', fontWeight: 'bold' }}>
                      {user.email}
                    </Typography>
                  </Tooltip>
                </Box>
              </Box>

              <Chip
                size="small"
                label={user.is_active ? (user.subscription_status === 'active' ? 'Premium' : 'Free') : 'Inactive'}
                color={user.subscription_status === 'active' ? 'default' : 'default'}
                sx={{ 
                  fontSize: '0.7rem', 
                  height: 20, 
                  mb: 1,
                  ...(user.subscription_status === 'active' && {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                  })
                }}
              />

              <Button
                size="small"
                variant="contained"
                startIcon={<LogoutIcon fontSize="small" />}
                onClick={handleLogout}
                fullWidth
                sx={{
                  backgroundColor: theme.palette.primary.main,
                  color: theme.palette.primary.contrastText,
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                  },
                }}
              >
                {t('signOut')}
              </Button>
            </Box>
          </Box>
        ) : !authLoading ? (
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 1, px: 1 }}>
            <Button
              size="small"
              variant="outlined"
              startIcon={<LoginIcon />}
              onClick={() => navigate('/login')}
              sx={{
                backgroundColor: 'transparent',
                color: theme.palette.sidebarText,
                borderColor: 'rgba(255, 255, 255, 0.1)',
                justifyContent: 'flex-start',
                borderRadius: '0.375rem',
                textTransform: 'none',
                padding: '8px 16px',
                '&:hover': {
                  backgroundColor: theme.palette.sidebarHoverBg,
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
            >
              {t('signIn')}
            </Button>
          </Box>
        ) : (
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <CircularProgress size={24} sx={{ color: theme.palette.primary.main }} />
          </Box>
        )}
      </Box>


    </Box>
  );
};

export default Sidebar;
