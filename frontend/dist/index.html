<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>STEELNET.AI</title>
    <script>
      // Disable HMR to avoid WebSocket issues
      window.__VITE_DISABLE_HMR = true;

      // Set API URLs - use dynamic hostname and protocol detection
      const currentHost = window.location.hostname;
      const currentProtocol = window.location.protocol;

      // For production servers, always use HTTP for backend API calls
      // since the backend doesn't support HTTPS yet
      const apiProtocol = (currentHost === '************' || currentHost === 'steelnet.ai')
        ? 'http:'
        : currentProtocol;

      window.API_BASE_URL = currentProtocol + "//" + currentHost + ":5173";
      window.BACKEND_URL = apiProtocol + "//" + currentHost + ":8000";

      console.log("Using dynamic API configuration:");
      console.log("- Frontend protocol:", currentProtocol);
      console.log("- API protocol:", apiProtocol);
      console.log("- Host:", currentHost);
      console.log("- Frontend URL:", window.API_BASE_URL);
      console.log("- Backend URL:", window.BACKEND_URL);

      // Fix React issues
      window.__vite_plugin_react_preamble_installed__ = true;
    </script>
    <script type="module" crossorigin src="/assets/index-CxR_VJwh.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/emotion-CXuTP4y4.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-CXWNGN5T.js">
    <link rel="modulepreload" crossorigin href="/assets/router-BApoKN__.js">
    <link rel="modulepreload" crossorigin href="/assets/material-Cf1GG4R-.js">
    <link rel="modulepreload" crossorigin href="/assets/icons-DzyLz7DY.js">
    <link rel="stylesheet" crossorigin href="/assets/index-BL0LZ99n.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
